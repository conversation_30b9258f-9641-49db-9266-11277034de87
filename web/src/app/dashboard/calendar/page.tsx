import { ContentCalendar } from '@/components/calendar/ContentCalendar';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export default async function CalendarPage() {
  const cookieStore = cookies();

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  const {
    data: { user },
  } = await supabase.auth.getUser();

  // The dashboard layout already handles authentication,
  // so we can assume user exists here
  if (!user) {
    return null;
  }

  return <ContentCalendar userId={user.id} />;
}
