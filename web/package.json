{"name": "grosonix-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.39.3", "autoprefixer": "^10.4.17", "axios": "^1.6.7", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "framer-motion": "^12.18.1", "lucide-react": "^0.514.0", "next": "14.1.0", "next-swagger-doc": "^0.4.0", "openai": "^4.28.0", "postcss": "^8.4.33", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-confetti-explosion": "^3.0.3", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "sonner": "^2.0.5", "swagger-ui-react": "^5.10.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.1", "twitter-api-v2": "^1.15.2"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/swagger-ui-react": "^4.18.3", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "typescript": "^5.3.3"}}