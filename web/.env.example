VITE_SUPABASE_ANON_KEY=
VITE_SUPABASE_URL=

NEXT_PUBLIC_SUPABASE_ANON_KEY=
NEXT_PUBLIC_SUPABASE_URL=

NEXT_PUBLIC_APP_URL=http://localhost:4001

# Supabase Service Role Key (for server-side operations that bypass R<PERSON>)
SUPABASE_SERVICE_ROLE_KEY=

# Twitter API v2 (Required for Twitter integration)
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret

# Instagram Graph API via Facebook (Required for Instagram Business/Creator integration)
# Note: These are actually Facebook App credentials that provide Instagram access
INSTAGRAM_CLIENT_ID=your_facebook_app_id
INSTAGRAM_CLIENT_SECRET=your_facebook_app_secret
LINKEDIN_REDIRECT_URI=
LINKEDIN_SCOPES=

# LinkedIn API
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret

# OpenAI API (Required for AI content generation)
OPENAI_API_KEY=
