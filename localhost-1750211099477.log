main-world.63f7dde8.js:1 Injecting PWR Provider...
main-world.63f7dde8.js:1 PWR Provider injected successfully pwr 0.9.0
handle-main-world.c3dbfe7f.js:1 Content script loaded in main world
hook.js:608 Warning: Text content did not match. Server: "2:44:48 AM" Client: "02:44:54" Error Component Stack
    at span (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at RealTimeMetrics (animated-counter.tsx:171:3)
    at div (<anonymous>)
    at _c8 (card.tsx:57:6)
    at div (<anonymous>)
    at _c (card.tsx:7:6)
    at StatsGrid (StatsGrid.tsx:38:3)
    at div (<anonymous>)
    at DashboardMetrics (DashboardMetrics.tsx:25:36)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at InnerLayoutRouter (layout-router.js:242:11)
    at RedirectErrorBoundary (redirect-boundary.js:73:9)
    at RedirectBoundary (redirect-boundary.js:81:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:335:11)
    at ErrorBoundary (error-boundary.js:161:11)
    at InnerScrollAndFocusHandler (layout-router.js:152:9)
    at ScrollAndFocusHandler (layout-router.js:227:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:354:11)
    at div (<anonymous>)
    at main (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at InnerLayoutRouter (layout-router.js:242:11)
    at RedirectErrorBoundary (redirect-boundary.js:73:9)
    at RedirectBoundary (redirect-boundary.js:81:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:335:11)
    at ErrorBoundary (error-boundary.js:161:11)
    at InnerScrollAndFocusHandler (layout-router.js:152:9)
    at ScrollAndFocusHandler (layout-router.js:227:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:354:11)
    at ThemeProvider (ThemeProvider.tsx:28:33)
    at body (<anonymous>)
    at html (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:73:9)
    at RedirectBoundary (redirect-boundary.js:81:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:84:9)
    at HotReload (hot-reloader-client.js:307:11)
    at Router (app-router.js:181:11)
    at ErrorBoundaryHandler (error-boundary.js:114:9)
    at ErrorBoundary (error-boundary.js:161:11)
    at AppRouter (app-router.js:536:13)
    at ServerRoot (app-index.js:129:11)
    at RSCComponent (<anonymous>)
    at Root (app-index.js:145:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:35
console.error @ hydration-error-info.js:41
printWarning @ react-dom.development.js:75
error @ react-dom.development.js:53
checkForUnmatchedText @ react-dom.development.js:27698
didNotMatchHydratedTextInstance @ react-dom.development.js:30493
prepareToHydrateHostTextInstance @ react-dom.development.js:6731
completeWork @ react-dom.development.js:17046
completeUnitOfWork @ react-dom.development.js:22029
performUnitOfWork @ react-dom.development.js:21861
workLoopConcurrent @ react-dom.development.js:21840
renderRootConcurrent @ react-dom.development.js:21808
performConcurrentWorkOnRoot @ react-dom.development.js:20841
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
hook.js:608 Warning: An error occurred during hydration. The server HTML was replaced with client content in <#document>.
overrideMethod @ hook.js:608
window.console.error @ app-index.js:35
console.error @ hydration-error-info.js:41
printWarning @ react-dom.development.js:75
error @ react-dom.development.js:53
errorHydratingContainer @ react-dom.development.js:30574
recoverFromConcurrentError @ react-dom.development.js:20914
performConcurrentWorkOnRoot @ react-dom.development.js:20871
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
on-recoverable-error.js:20 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2:44:48 AM" Client: "02:44:54"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:27705:23)
    at didNotMatchHydratedTextInstance (react-dom.development.js:30493:17)
    at prepareToHydrateHostTextInstance (react-dom.development.js:6731:33)
    at completeWork (react-dom.development.js:17046:37)
    at completeUnitOfWork (react-dom.development.js:22029:28)
    at performUnitOfWork (react-dom.development.js:21861:17)
    at workLoopConcurrent (react-dom.development.js:21840:17)
    at renderRootConcurrent (react-dom.development.js:21808:25)
    at performConcurrentWorkOnRoot (react-dom.development.js:20841:48)
    at workLoop (scheduler.development.js:200:48)
    at flushWork (scheduler.development.js:178:28)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:416:35)
checkForUnmatchedText @ react-dom.development.js:27705
didNotMatchHydratedTextInstance @ react-dom.development.js:30493
prepareToHydrateHostTextInstance @ react-dom.development.js:6731
completeWork @ react-dom.development.js:17046
completeUnitOfWork @ react-dom.development.js:22029
performUnitOfWork @ react-dom.development.js:21861
workLoopConcurrent @ react-dom.development.js:21840
renderRootConcurrent @ react-dom.development.js:21808
performConcurrentWorkOnRoot @ react-dom.development.js:20841
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
on-recoverable-error.js:20 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2:44:48 AM" Client: "02:44:54"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:27705:23)
    at didNotMatchHydratedTextInstance (react-dom.development.js:30493:17)
    at prepareToHydrateHostTextInstance (react-dom.development.js:6731:33)
    at completeWork (react-dom.development.js:17046:37)
    at completeUnitOfWork (react-dom.development.js:22029:28)
    at performUnitOfWork (react-dom.development.js:21861:17)
    at workLoopConcurrent (react-dom.development.js:21840:17)
    at renderRootConcurrent (react-dom.development.js:21808:25)
    at performConcurrentWorkOnRoot (react-dom.development.js:20841:48)
    at workLoop (scheduler.development.js:200:48)
    at flushWork (scheduler.development.js:178:28)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:416:35)
checkForUnmatchedText @ react-dom.development.js:27705
didNotMatchHydratedTextInstance @ react-dom.development.js:30493
prepareToHydrateHostTextInstance @ react-dom.development.js:6731
completeWork @ react-dom.development.js:17046
completeUnitOfWork @ react-dom.development.js:22029
performUnitOfWork @ react-dom.development.js:21861
workLoopConcurrent @ react-dom.development.js:21840
renderRootConcurrent @ react-dom.development.js:21808
performConcurrentWorkOnRoot @ react-dom.development.js:20841
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
on-recoverable-error.js:20 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2:44:48 AM" Client: "02:44:54"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:27705:23)
    at didNotMatchHydratedTextInstance (react-dom.development.js:30493:17)
    at prepareToHydrateHostTextInstance (react-dom.development.js:6731:33)
    at completeWork (react-dom.development.js:17046:37)
    at completeUnitOfWork (react-dom.development.js:22029:28)
    at performUnitOfWork (react-dom.development.js:21861:17)
    at workLoopConcurrent (react-dom.development.js:21840:17)
    at renderRootConcurrent (react-dom.development.js:21808:25)
    at performConcurrentWorkOnRoot (react-dom.development.js:20841:48)
    at workLoop (scheduler.development.js:200:48)
    at flushWork (scheduler.development.js:178:28)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:416:35)
checkForUnmatchedText @ react-dom.development.js:27705
didNotMatchHydratedTextInstance @ react-dom.development.js:30493
prepareToHydrateHostTextInstance @ react-dom.development.js:6731
completeWork @ react-dom.development.js:17046
completeUnitOfWork @ react-dom.development.js:22029
performUnitOfWork @ react-dom.development.js:21861
workLoopConcurrent @ react-dom.development.js:21840
renderRootConcurrent @ react-dom.development.js:21808
performConcurrentWorkOnRoot @ react-dom.development.js:20841
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
on-recoverable-error.js:20 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2:44:48 AM" Client: "02:44:54"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:27705:23)
    at didNotMatchHydratedTextInstance (react-dom.development.js:30493:17)
    at prepareToHydrateHostTextInstance (react-dom.development.js:6731:33)
    at completeWork (react-dom.development.js:17046:37)
    at completeUnitOfWork (react-dom.development.js:22029:28)
    at performUnitOfWork (react-dom.development.js:21861:17)
    at workLoopConcurrent (react-dom.development.js:21840:17)
    at renderRootConcurrent (react-dom.development.js:21808:25)
    at performConcurrentWorkOnRoot (react-dom.development.js:20841:48)
    at workLoop (scheduler.development.js:200:48)
    at flushWork (scheduler.development.js:178:28)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:416:35)
checkForUnmatchedText @ react-dom.development.js:27705
didNotMatchHydratedTextInstance @ react-dom.development.js:30493
prepareToHydrateHostTextInstance @ react-dom.development.js:6731
completeWork @ react-dom.development.js:17046
completeUnitOfWork @ react-dom.development.js:22029
performUnitOfWork @ react-dom.development.js:21861
workLoopConcurrent @ react-dom.development.js:21840
renderRootConcurrent @ react-dom.development.js:21808
performConcurrentWorkOnRoot @ react-dom.development.js:20841
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
on-recoverable-error.js:20 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2:44:48 AM" Client: "02:44:54"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:27705:23)
    at didNotMatchHydratedTextInstance (react-dom.development.js:30493:17)
    at prepareToHydrateHostTextInstance (react-dom.development.js:6731:33)
    at completeWork (react-dom.development.js:17046:37)
    at completeUnitOfWork (react-dom.development.js:22029:28)
    at performUnitOfWork (react-dom.development.js:21861:17)
    at workLoopConcurrent (react-dom.development.js:21840:17)
    at renderRootConcurrent (react-dom.development.js:21808:25)
    at performConcurrentWorkOnRoot (react-dom.development.js:20841:48)
    at workLoop (scheduler.development.js:200:48)
    at flushWork (scheduler.development.js:178:28)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:416:35)
checkForUnmatchedText @ react-dom.development.js:27705
didNotMatchHydratedTextInstance @ react-dom.development.js:30493
prepareToHydrateHostTextInstance @ react-dom.development.js:6731
completeWork @ react-dom.development.js:17046
completeUnitOfWork @ react-dom.development.js:22029
performUnitOfWork @ react-dom.development.js:21861
workLoopConcurrent @ react-dom.development.js:21840
renderRootConcurrent @ react-dom.development.js:21808
performConcurrentWorkOnRoot @ react-dom.development.js:20841
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
on-recoverable-error.js:20 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2:44:48 AM" Client: "02:44:54"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:27705:23)
    at didNotMatchHydratedTextInstance (react-dom.development.js:30493:17)
    at prepareToHydrateHostTextInstance (react-dom.development.js:6731:33)
    at completeWork (react-dom.development.js:17046:37)
    at completeUnitOfWork (react-dom.development.js:22029:28)
    at performUnitOfWork (react-dom.development.js:21861:17)
    at workLoopConcurrent (react-dom.development.js:21840:17)
    at renderRootConcurrent (react-dom.development.js:21808:25)
    at performConcurrentWorkOnRoot (react-dom.development.js:20841:48)
    at workLoop (scheduler.development.js:200:48)
    at flushWork (scheduler.development.js:178:28)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:416:35)
checkForUnmatchedText @ react-dom.development.js:27705
didNotMatchHydratedTextInstance @ react-dom.development.js:30493
prepareToHydrateHostTextInstance @ react-dom.development.js:6731
completeWork @ react-dom.development.js:17046
completeUnitOfWork @ react-dom.development.js:22029
performUnitOfWork @ react-dom.development.js:21861
workLoopConcurrent @ react-dom.development.js:21840
renderRootConcurrent @ react-dom.development.js:21808
performConcurrentWorkOnRoot @ react-dom.development.js:20841
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
react-dom.development.js:14381 Uncaught Error: There was an error while hydrating. Because the error happened outside of a Suspense boundary, the entire root will switch to client rendering.
    at updateHostRoot (react-dom.development.js:14381:71)
    at beginWork$1 (react-dom.development.js:15958:28)
    at beginWork (react-dom.development.js:22789:28)
    at performUnitOfWork (react-dom.development.js:21852:24)
    at workLoopSync (react-dom.development.js:21617:17)
    at renderRootSync (react-dom.development.js:21584:21)
    at recoverFromConcurrentError (react-dom.development.js:20917:30)
    at performConcurrentWorkOnRoot (react-dom.development.js:20871:46)
    at workLoop (scheduler.development.js:200:48)
    at flushWork (scheduler.development.js:178:28)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:416:35)
updateHostRoot @ react-dom.development.js:14381
beginWork$1 @ react-dom.development.js:15958
beginWork @ react-dom.development.js:22789
performUnitOfWork @ react-dom.development.js:21852
workLoopSync @ react-dom.development.js:21617
renderRootSync @ react-dom.development.js:21584
recoverFromConcurrentError @ react-dom.development.js:20917
performConcurrentWorkOnRoot @ react-dom.development.js:20871
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
performance-optimizer.ts:285 Long task detected: 679ms
overrideMethod @ hook.js:608
eval @ performance-optimizer.ts:285
performance-optimizer.ts:285 Long task detected: 72ms
overrideMethod @ hook.js:608
eval @ performance-optimizer.ts:285
performance-optimizer.ts:285 Long task detected: 249ms
overrideMethod @ hook.js:608
eval @ performance-optimizer.ts:285
performance-optimizer.ts:285 Long task detected: 91ms
overrideMethod @ hook.js:608
eval @ performance-optimizer.ts:285
performance-optimizer.ts:285 Long task detected: 69ms
overrideMethod @ hook.js:608
eval @ performance-optimizer.ts:285
GoalSetting.tsx:44 
            
            
           GET http://localhost:4001/api/goals 500 (Internal Server Error)
fetchGoals @ GoalSetting.tsx:44
eval @ GoalSetting.tsx:38
commitHookEffectListMount @ react-dom.development.js:18071
commitHookPassiveMountEffects @ react-dom.development.js:19742
commitPassiveMountOnFiber @ react-dom.development.js:19826
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19824
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19918
recursivelyTraversePassiveMountEffects @ react-dom.development.js:19808
commitPassiveMountOnFiber @ react-dom.development.js:19832
commitPassiveMountEffects @ react-dom.development.js:19799
flushPassiveEffectsImpl @ react-dom.development.js:22446
flushPassiveEffects @ react-dom.development.js:22398
performSyncWorkOnRoot @ react-dom.development.js:21110
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:9119
flushSyncWorkOnAllRoots @ react-dom.development.js:9085
commitRootImpl @ react-dom.development.js:22339
commitRoot @ react-dom.development.js:22114
commitRootWhenReady @ react-dom.development.js:21044
finishConcurrentRender @ react-dom.development.js:21015
performConcurrentWorkOnRoot @ react-dom.development.js:20885
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
GoalSetting.tsx:44 
            
            
           GET http://localhost:4001/api/goals 500 (Internal Server Error)
fetchGoals @ GoalSetting.tsx:44
eval @ GoalSetting.tsx:38
commitHookEffectListMount @ react-dom.development.js:18071
invokePassiveEffectMountInDEV @ react-dom.development.js:20423
invokeEffectsInDev @ react-dom.development.js:22728
legacyCommitDoubleInvokeEffectsInDEV @ react-dom.development.js:22715
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:22700
flushPassiveEffectsImpl @ react-dom.development.js:22459
flushPassiveEffects @ react-dom.development.js:22398
performSyncWorkOnRoot @ react-dom.development.js:21110
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:9119
flushSyncWorkOnAllRoots @ react-dom.development.js:9085
commitRootImpl @ react-dom.development.js:22339
commitRoot @ react-dom.development.js:22114
commitRootWhenReady @ react-dom.development.js:21044
finishConcurrentRender @ react-dom.development.js:21015
performConcurrentWorkOnRoot @ react-dom.development.js:20885
workLoop @ scheduler.development.js:200
flushWork @ scheduler.development.js:178
performWorkUntilDeadline @ scheduler.development.js:416
css-generic.js:1 uBOL: Generic cosmetic filtering stopped because no more DOM changes
